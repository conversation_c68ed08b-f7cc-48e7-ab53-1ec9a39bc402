import React, { useEffect, useRef } from 'react';
import { Box } from '@mantine/core';
import { Controller, Control, UseFormGetValues, UseFormSetValue, UseFormSetFocus } from 'react-hook-form';
import { KanbanCheckbox, KanbanTitle } from 'kanban-design-system';
import { IconTrash } from '@tabler/icons-react';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { EXECUTION_API_DESCRIPTION_MAX_LENGTH, EXECUTION_API_KEY_MAX_LENGTH } from '@common/constants/ValidationConstant';
import classes from './RenderTable.module.css';
import ParamAwareInput from '@pages/admins/executionConfig/tabs/execution/components/ParamAwareInput';

export interface KeyValueRow {
  id: string;
  key: string;
  value: string;
  description: string;
  enable: boolean;
  autoGenerated?: boolean;
}

interface RenderTableProps {
  fa: {
    fields: any[];
    append: (value: any) => void;
    remove: (index: number) => void;
  };
  control: Control<{ apiInfo: ExecutionApiInfoModel }>;
  namePrefix: 'apiInfo.headers' | 'apiInfo.params' | 'apiInfo.body.formUrlEncoded';
  isViewMode: boolean;
  executionParams?: string[];
  getValues: UseFormGetValues<{ apiInfo: ExecutionApiInfoModel }>;
  setValue: UseFormSetValue<{ apiInfo: ExecutionApiInfoModel }>;
  registerEditor?: (fieldName: string, insertFn: (text: string, pos: number) => void) => void;
  setFocus?: UseFormSetFocus<{ apiInfo: ExecutionApiInfoModel }>;
  onFieldChange?: () => void;
}

const COLUMNS: (keyof KeyValueRow)[] = ['key', 'value', 'description'];

const DEFAULT_KEY_VALUE = {
  key: '',
  value: '',
  description: '',
  enable: true,
};

export const RenderTable: React.FC<RenderTableProps> = ({
  control,
  executionParams = [],
  fa,
  isViewMode,
  namePrefix,
  onFieldChange,
  registerEditor,
  setFocus,
  setValue,
}) => {
  const shouldDisableRow = (field: KeyValueRow) => namePrefix === 'apiInfo.headers' && field.autoGenerated;

  const editorRefs = useRef<Record<number, Record<string, { focus: () => void }>>>({});
  const currentFocusRef = useRef<{ focus: () => void } | null>(null);

  const handleAutoAppend = (idx: number, col: string) => {
    if (idx === fa.fields.length - 1) {
      currentFocusRef.current = editorRefs.current[idx]?.[col] ?? null;
      fa.append(DEFAULT_KEY_VALUE);
    }
  };

  useEffect(() => {
    if (currentFocusRef.current) {
      setTimeout(() => {
        currentFocusRef.current?.focus();
        currentFocusRef.current = null;
      }, 100);
    }
  }, [fa.fields.length]);

  // Ensure at least one row exists
  useEffect(() => {
    if (fa.fields.length === 0) {
      fa.append(DEFAULT_KEY_VALUE);
    }
  }, [fa]);

  return (
    <Box className={classes.tableWrap}>
      {/* Header */}
      <Box className={classes.tableHeader}>
        <Box className={classes.tableHeaderCell}></Box>
        {COLUMNS.map((col) => (
          <Box key={col} className={classes.tableHeaderCell}>
            <KanbanTitle fw={600} size='sm'>
              {col.charAt(0).toUpperCase() + col.slice(1)}
            </KanbanTitle>
          </Box>
        ))}
        <Box className={classes.tableHeaderCell}></Box>
      </Box>

      {/* Rows */}
      {fa.fields.map((field: KeyValueRow, idx: number) => {
        const isAutoGenerated = shouldDisableRow(field);

        return (
          <Box key={field.id} className={classes.tableRow}>
            {/* Checkbox */}
            <Box className={classes.tableCell}>
              <Controller
                name={`${namePrefix}.${idx}.enable` as any}
                control={control}
                render={({ field: { name, onBlur, onChange, ref, value } }) => (
                  <Box className={classes.checkboxContainer}>
                    <KanbanCheckbox
                      name={name}
                      checked={isAutoGenerated ? true : !!value}
                      onChange={(e) => {
                        if (!isAutoGenerated) {
                          onChange(e.currentTarget.checked);
                          onFieldChange?.();
                        }
                      }}
                      onBlur={onBlur}
                      disabled={isAutoGenerated || isViewMode}
                      ref={ref}
                    />
                  </Box>
                )}
              />
            </Box>

            {/* Key, Value, Description columns */}
            {COLUMNS.map((col) => (
              <Box key={col} className={classes.tableCell}>
                <Controller
                  name={`${namePrefix}.${idx}.${col}` as any}
                  control={control}
                  render={({ field }) => (
                    <ParamAwareInput
                      field={{
                        ...field,
                        value: field.value || '',
                        onChange: (e) => {
                          field.onChange(e.target.value);
                          onFieldChange?.();
                        },
                      }}
                      ref={(ref) => {
                        if (!editorRefs.current[idx]) {
                          editorRefs.current[idx] = {};
                        }
                        editorRefs.current[idx][col] = ref;
                      }}
                      disabled={isViewMode || isAutoGenerated}
                      executionParams={executionParams}
                      registerEditor={registerEditor}
                      maxLength={col === 'key' || col === 'value' ? EXECUTION_API_KEY_MAX_LENGTH : EXECUTION_API_DESCRIPTION_MAX_LENGTH}
                    />
                  )}
                />
              </Box>
            ))}

            {/* Delete */}
            <Box className={classes.tableCell}>
              <Box className={classes.actionContainer}>
                {!isViewMode && fa.fields.length > 1 && !isAutoGenerated && idx !== fa.fields.length - 1 && (
                  <IconTrash
                    size={16}
                    className={classes.deleteIcon}
                    onClick={() => {
                      fa.remove(idx);
                      onFieldChange?.();
                    }}
                    style={{ cursor: 'pointer', color: 'black' }}
                  />
                )}
              </Box>
            </Box>
          </Box>
        );
      })}
    </Box>
  );
};
